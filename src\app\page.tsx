'use client';

import VideoCard from './components/VideoCard';
import CategoryCard from './components/CategoryCard';
import RecommendedVideos from './components/RecommendedVideos';
import Link from 'next/link';
import { FaF<PERSON>, FaClock, FaChevronRight, FaThumbsUp } from 'react-icons/fa';
import { useState, useEffect } from 'react';
import axios from 'axios';

// Mock data for demonstration
const featuredVideos = [
  {
    id: '1',
    title: 'Hot blonde in action with her boyfriend',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 845, // 14:05
    views: 1250000,
    likes: 24500,
    slug: 'hot-blonde-in-action'
  },
  {
    id: '2',
    title: '<PERSON>runette gets wild in the bedroom',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1230, // 20:30
    views: 980000,
    likes: 18700,
    slug: 'brunette-gets-wild'
  },
  {
    id: '3',
    title: 'Steamy shower scene with two girls',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 720, // 12:00
    views: 1500000,
    likes: 32000,
    slug: 'steamy-shower-scene'
  },
  {
    id: '4',
    title: 'Office romance turns into something more',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 950, // 15:50
    views: 750000,
    likes: 15600,
    slug: 'office-romance'
  },
];

const trendingVideos = [
  {
    id: '5',
    title: 'Passionate couple on vacation',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1120, // 18:40
    views: 2100000,
    likes: 45000,
    slug: 'passionate-couple-vacation'
  },
  {
    id: '6',
    title: 'Fitness instructor gives special lesson',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 890, // 14:50
    views: 1800000,
    likes: 38000,
    slug: 'fitness-instructor-lesson'
  },
  {
    id: '7',
    title: 'Massage turns into something unexpected',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1350, // 22:30
    views: 2500000,
    likes: 52000,
    slug: 'massage-unexpected'
  },
  {
    id: '8',
    title: 'Pool party gets wild with multiple girls',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1680, // 28:00
    views: 3200000,
    likes: 68000,
    slug: 'pool-party-wild'
  },
];

const popularCategories = [
  {
    name: 'Amateur',
    slug: 'amateur',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Amateur',
    videoCount: 12500
  },
  {
    name: 'MILF',
    slug: 'milf',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=MILF',
    videoCount: 8700
  },
  {
    name: 'Lesbian',
    slug: 'lesbian',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Lesbian',
    videoCount: 9300
  },
  {
    name: 'Threesome',
    slug: 'threesome',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Threesome',
    videoCount: 5600
  },
  {
    name: 'Blonde',
    slug: 'blonde',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Blonde',
    videoCount: 7800
  },
  {
    name: 'Anal',
    slug: 'anal',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Anal',
    videoCount: 6200
  },
];

export default function Home() {
  const [featuredVideosData, setFeaturedVideosData] = useState<any[]>([]);
  const [popularCategoriesData, setPopularCategoriesData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        setLoading(true);

        // Fetch featured videos
        const featuredResponse = await axios.get('/api/videos?sort=featured&limit=4');
        if (featuredResponse.data && featuredResponse.data.videos) {
          setFeaturedVideosData(featuredResponse.data.videos);
        } else {
          // Fallback to mock data if API fails
          setFeaturedVideosData(featuredVideos);
        }

        // Fetch popular categories
        const categoriesResponse = await axios.get('/api/categories?withCounts=true&limit=6');
        if (categoriesResponse.data && categoriesResponse.data.categories) {
          setPopularCategoriesData(categoriesResponse.data.categories);
        } else {
          // Fallback to mock data if API fails
          setPopularCategoriesData(popularCategories);
        }
      } catch (error) {
        console.error('Error fetching home data:', error);
        // Fallback to mock data if API fails
        setFeaturedVideosData(featuredVideos);
        setPopularCategoriesData(popularCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  return (
    <div className="min-h-screen bg-ph-black-950">
      {/* Hero Section */}
      <section className="py-12">
        <div className="container-ph">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              The World's Largest Adult Site
            </h1>
            <p className="text-xl text-ph-gray-400 mb-8 max-w-3xl mx-auto">
              Millions of videos updated daily. Discover trending content and explore categories.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/categories"
                className="btn-ph-primary"
              >
                Browse Categories
              </Link>
              <Link
                href="/trending"
                className="btn-ph-secondary"
              >
                <FaFire className="mr-2" />
                View Trending
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Recommended Videos Section */}
      <section className="mb-12">
        <div className="container-ph">
          <RecommendedVideos limit={4} title="Recommended For You" />
        </div>
      </section>

      {/* Featured Videos Section */}
      <section className="mb-12">
        <div className="container-ph">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center">
              <FaFire className="text-ph-orange-500 mr-3" />
              Featured Videos
            </h2>
            <Link
              href="/featured"
              className="btn-ph-secondary text-sm"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="video-grid-mobile">
            {!loading ? (
              featuredVideosData.map((video) => (
                <VideoCard
                  key={video._id}
                  video={video}
                />
              ))
            ) : (
              // Show skeleton loaders while loading
              Array(4).fill(0).map((_, index) => (
                <div key={index} className="card-ph animate-pulse">
                  <div className="aspect-video bg-ph-gray-700"></div>
                  <div className="p-4">
                    <div className="h-4 bg-ph-gray-700 rounded mb-3"></div>
                    <div className="h-3 bg-ph-gray-700 rounded w-2/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Popular Categories Section */}
      <section className="mb-12">
        <div className="container-ph">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center">
              <FaClock className="text-ph-orange-500 mr-3" />
              Popular Categories
            </h2>
            <Link
              href="/categories"
              className="btn-ph-secondary text-sm"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {!loading ? (
              popularCategoriesData.map((category) => (
                <CategoryCard
                  key={category._id || category.slug}
                  id={category._id || ''}
                  name={category.name}
                  slug={category.slug}
                  thumbnail={category.thumbnailUrl}
                  videoCount={category.videoCount || 0}
                  description={category.description || ''}
                />
              ))
            ) : (
              // Show skeleton loaders while loading
              Array(6).fill(0).map((_, index) => (
                <div key={index} className="card-ph animate-pulse">
                  <div className="aspect-video bg-ph-gray-700"></div>
                  <div className="p-4">
                    <div className="h-4 bg-ph-gray-700 rounded mb-3"></div>
                    <div className="h-3 bg-ph-gray-700 rounded w-1/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Most Liked Videos Section */}
      <section className="mb-12">
        <div className="container-ph">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center">
              <FaThumbsUp className="text-ph-orange-500 mr-3" />
              Most Liked
            </h2>
            <Link
              href="/search?sort=most-liked"
              className="btn-ph-secondary text-sm"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="video-grid-mobile">
            {trendingVideos.map((video) => (
              <VideoCard
                key={video.id}
                id={video.id}
                title={video.title}
                thumbnail={video.thumbnail}
                duration={video.duration}
                views={video.views}
                likes={video.likes}
                slug={video.slug}
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
