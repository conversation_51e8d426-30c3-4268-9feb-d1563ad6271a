// Utility functions for generating placeholder images and sample data

export const SAMPLE_THUMBNAILS = {
  videos: [
    '/images/placeholders/video-thumb-1.svg',
    '/images/placeholders/video-thumb-2.svg',
    '/images/placeholders/video-thumb-3.svg',
    '/images/placeholders/video-thumb-4.svg',
    '/images/placeholders/video-thumb-5.svg',
    '/images/placeholders/video-thumb-6.svg',
    '/images/placeholders/video-thumb-7.svg',
    '/images/placeholders/video-thumb-8.svg',
    '/images/placeholders/video-thumb-9.svg',
    '/images/placeholders/video-thumb-10.svg',
    '/images/placeholders/video-thumb-11.svg',
    '/images/placeholders/video-thumb-12.svg',
  ],
  categories: [
    '/images/placeholders/category-1.svg',
    '/images/placeholders/category-2.svg',
    '/images/placeholders/category-3.svg',
    '/images/placeholders/category-4.svg',
    '/images/placeholders/category-5.svg',
    '/images/placeholders/category-6.svg',
    '/images/placeholders/category-7.svg',
    '/images/placeholders/category-8.svg',
  ]
};

// Generate a consistent placeholder based on ID or title
export const getVideoPlaceholder = (id?: string, title?: string): string => {
  const seed = id || title || 'default';
  const hash = seed.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  const index = Math.abs(hash) % SAMPLE_THUMBNAILS.videos.length;
  return SAMPLE_THUMBNAILS.videos[index];
};

export const getCategoryPlaceholder = (id?: string, name?: string): string => {
  const seed = id || name || 'default';
  const hash = seed.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  const index = Math.abs(hash) % SAMPLE_THUMBNAILS.categories.length;
  return SAMPLE_THUMBNAILS.categories[index];
};

// Generate placeholder URL with custom text and colors
export const generatePlaceholderUrl = (
  width: number = 640,
  height: number = 360,
  text?: string,
  bgColor: string = '1a1a1a',
  textColor: string = 'ffffff'
): string => {
  const encodedText = text ? encodeURIComponent(text) : 'Video+Thumbnail';
  return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodedText}`;
};

// Generate gradient placeholder with purple/blue theme
export const getGradientPlaceholder = (seed?: string): string => {
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  ];
  
  if (seed) {
    const hash = seed.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    const index = Math.abs(hash) % gradients.length;
    return gradients[index];
  }
  
  return gradients[Math.floor(Math.random() * gradients.length)];
};

// Sample video data for development
export const SAMPLE_VIDEOS = [
  {
    _id: 'sample-1',
    title: 'Amazing Video Content 1',
    slug: 'amazing-video-content-1',
    duration: 720,
    views: 15420,
    likes: 89,
    thumbnailUrl: '',
    categories: [{ _id: 'cat-1', name: 'Popular', slug: 'popular' }],
    tags: ['trending', 'popular'],
    createdAt: new Date().toISOString(),
  },
  {
    _id: 'sample-2',
    title: 'Incredible Experience 2',
    slug: 'incredible-experience-2',
    duration: 1080,
    views: 23150,
    likes: 92,
    thumbnailUrl: '',
    categories: [{ _id: 'cat-2', name: 'Featured', slug: 'featured' }],
    tags: ['featured', 'quality'],
    createdAt: new Date().toISOString(),
  },
  {
    _id: 'sample-3',
    title: 'Premium Content 3',
    slug: 'premium-content-3',
    duration: 900,
    views: 18750,
    likes: 95,
    thumbnailUrl: '',
    categories: [{ _id: 'cat-3', name: 'Premium', slug: 'premium' }],
    tags: ['premium', 'exclusive'],
    createdAt: new Date().toISOString(),
  },
];

// Sample category data
export const SAMPLE_CATEGORIES = [
  {
    _id: 'cat-1',
    name: 'Popular',
    slug: 'popular',
    description: 'Most popular content',
    thumbnailUrl: '',
    videoCount: 1250,
    isActive: true,
  },
  {
    _id: 'cat-2',
    name: 'Featured',
    slug: 'featured',
    description: 'Featured premium content',
    thumbnailUrl: '',
    videoCount: 890,
    isActive: true,
  },
  {
    _id: 'cat-3',
    name: 'Trending',
    slug: 'trending',
    description: 'Currently trending videos',
    thumbnailUrl: '',
    videoCount: 567,
    isActive: true,
  },
  {
    _id: 'cat-4',
    name: 'Premium',
    slug: 'premium',
    description: 'Exclusive premium content',
    thumbnailUrl: '',
    videoCount: 423,
    isActive: true,
  },
];
