'use client';

import Link from 'next/link';
import { Fa<PERSON>ye, FaThumbsUp, FaCalendarAlt, FaTrophy } from 'react-icons/fa';
import VideoThumbnail from './VideoThumbnail';
import { formatDuration, formatCount } from '@/app/utils/formatters';

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl?: string;
  embedUrl?: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface VideoCardProps {
  video: Video;
  isCompact?: boolean;
  showRank?: boolean;
  rank?: number;
  showUploadDate?: boolean;
  layout?: 'grid' | 'list';
  // Legacy props for backward compatibility
  id?: string;
  title?: string;
  thumbnail?: string;
  duration?: number;
  views?: number;
  likes?: number;
  slug?: string;
  videoUrl?: string;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  isCompact = false,
  showRank = false,
  rank,
  showUploadDate = false,
  layout = 'grid',
  // Legacy props for backward compatibility
  id,
  title,
  thumbnail,
  duration,
  views,
  likes,
  slug,
  videoUrl,
}) => {
  // Support both new video object and legacy props
  const videoData = video || {
    _id: id || '',
    title: title || '',
    thumbnailUrl: thumbnail || '',
    duration: duration || 0,
    views: views || 0,
    likes: likes || 0,
    slug: slug || '',
    createdAt: new Date().toISOString(),
    categories: [],
    tags: []
  };

  // Handle field name compatibility (thumbnail vs thumbnailUrl)
  const thumbnailSrc = videoData.thumbnailUrl || (video as any)?.thumbnail || thumbnail || '';
  const finalVideoData = {
    ...videoData,
    thumbnailUrl: thumbnailSrc
  };

  // List layout
  if (layout === 'list') {
    return (
      <div className="group bg-gray-900 rounded-lg overflow-hidden hover:bg-gray-800 transition-all duration-300">
        <Link href={`/videos/${finalVideoData.slug}`} className="flex">
          <div className="relative w-48 h-28 flex-shrink-0">
            <img
              src={finalVideoData.thumbnailUrl}
              alt={finalVideoData.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded font-medium">
              {formatDuration(finalVideoData.duration)}
            </div>
            {showRank && rank && (
              <div className="absolute top-2 left-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center">
                <FaTrophy className="mr-1" />
                #{rank}
              </div>
            )}
          </div>
          <div className="flex-1 p-4">
            <h3 className="text-white font-medium line-clamp-2 group-hover:text-orange-500 transition-colors duration-300 mb-2">
              {finalVideoData.title}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-gray-400">
              <span className="flex items-center">
                <FaEye className="mr-1" />
                {formatCount(finalVideoData.views)}
              </span>
              <span className="flex items-center">
                <FaThumbsUp className="mr-1" />
                {finalVideoData.likes}%
              </span>
              {showUploadDate && (
                <span className="flex items-center">
                  <FaCalendarAlt className="mr-1" />
                  {new Date(finalVideoData.createdAt).toLocaleDateString()}
                </span>
              )}
            </div>
            {finalVideoData.categories && finalVideoData.categories.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                {finalVideoData.categories.slice(0, 3).map((category) => (
                  <span key={category._id} className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">
                    {category.name}
                  </span>
                ))}
              </div>
            )}
          </div>
        </Link>
      </div>
    );
  }

  if (isCompact) {
    return (
      <div className="group">
        <Link href={`/videos/${finalVideoData.slug}`} className="block">
          <div className="relative overflow-hidden rounded-lg bg-ph-gray-800 hover:bg-ph-gray-700 transition-all duration-300 card-ph-hover">
            <img
              src={finalVideoData.thumbnailUrl}
              alt={finalVideoData.title}
              className="w-full aspect-video object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded font-medium">
              {formatDuration(finalVideoData.duration)}
            </div>
            {showRank && rank && (
              <div className="absolute top-2 left-2 bg-ph-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center">
                <FaTrophy className="mr-1" />
                #{rank}
              </div>
            )}
          </div>
          <div className="mt-3">
            <h3 className="text-white text-sm font-medium line-clamp-2 group-hover:text-ph-orange-500 transition-colors duration-300">
              {finalVideoData.title}
            </h3>
            <div className="flex items-center justify-between mt-2 text-xs text-ph-gray-400">
              <div className="flex items-center space-x-3">
                <span className="flex items-center">
                  <FaEye className="mr-1" />
                  {formatCount(finalVideoData.views)}
                </span>
                <span className="flex items-center">
                  <FaThumbsUp className="mr-1" />
                  {finalVideoData.likes}%
                </span>
              </div>
              {showUploadDate && (
                <span className="flex items-center text-ph-gray-500">
                  <FaCalendarAlt className="mr-1" />
                  {new Date(finalVideoData.createdAt).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </Link>
      </div>
    );
  }

  return (
    <div className="group">
      <Link href={`/videos/${finalVideoData.slug}`} className="block">
        <div className="card-ph card-ph-hover">
          <div className="relative overflow-hidden">
            {showRank && rank && (
              <div className="absolute top-3 left-3 bg-ph-orange-500 text-white text-sm px-3 py-1 rounded-full font-bold flex items-center z-10">
                <FaTrophy className="mr-1" />
                #{rank}
              </div>
            )}
            <VideoThumbnail
              title={finalVideoData.title}
              duration={finalVideoData.duration}
              thumbnail={finalVideoData.thumbnailUrl}
              videoUrl={finalVideoData.videoUrl}
              id={finalVideoData._id}
            />
          </div>

          <div className="p-4">
            <h3 className="text-white font-medium line-clamp-2 group-hover:text-ph-orange-500 transition-colors duration-300 mb-3">
              {finalVideoData.title}
            </h3>

            <div className="flex items-center justify-between text-ph-gray-400 text-sm">
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <FaEye className="mr-1" />
                  <span className="font-semibold">{formatCount(finalVideoData.views)}</span>
                </span>
                <span className="flex items-center text-green-400">
                  <FaThumbsUp className="mr-1" />
                  <span className="font-semibold">{finalVideoData.likes}%</span>
                </span>
              </div>
              {showUploadDate && (
                <span className="flex items-center text-ph-gray-500 text-xs">
                  <FaCalendarAlt className="mr-1" />
                  {new Date(finalVideoData.createdAt).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default VideoCard;
