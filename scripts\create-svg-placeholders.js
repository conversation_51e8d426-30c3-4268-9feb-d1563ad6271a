const fs = require('fs');
const path = require('path');

// Create placeholders directory if it doesn't exist
const placeholdersDir = path.join(__dirname, '../public/images/placeholders');
if (!fs.existsSync(placeholdersDir)) {
  fs.mkdirSync(placeholdersDir, { recursive: true });
}

// Function to create SVG placeholder
function createSVGPlaceholder(width, height, bgColor, textColor, text, filename) {
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad${filename}" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${bgColor};stop-opacity:1" />
      <stop offset="100%" style="stop-color:${adjustBrightness(bgColor, -20)};stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad${filename})"/>
  <text x="50%" y="45%" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="${textColor}">${text}</text>
  <text x="50%" y="60%" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="${textColor}" opacity="0.8">${width}x${height}</text>
  <circle cx="50%" cy="50%" r="30" fill="${textColor}" opacity="0.1"/>
  <polygon points="45%,40% 45%,60% 60%,50%" fill="${textColor}" opacity="0.7"/>
</svg>`;

  const filePath = path.join(placeholdersDir, filename);
  fs.writeFileSync(filePath, svg);
  console.log(`Created: ${filename}`);
}

// Function to adjust color brightness
function adjustBrightness(color, amount) {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

// Video thumbnail placeholders with purple/blue theme
const videoPlaceholders = [
  { bgColor: '#1a1a1a', textColor: '#ffffff', text: 'Video 1', filename: 'video-thumb-1.svg' },
  { bgColor: '#2d1b69', textColor: '#ffffff', text: 'Video 2', filename: 'video-thumb-2.svg' },
  { bgColor: '#3730a3', textColor: '#ffffff', text: 'Video 3', filename: 'video-thumb-3.svg' },
  { bgColor: '#4c1d95', textColor: '#ffffff', text: 'Video 4', filename: 'video-thumb-4.svg' },
  { bgColor: '#5b21b6', textColor: '#ffffff', text: 'Video 5', filename: 'video-thumb-5.svg' },
  { bgColor: '#6d28d9', textColor: '#ffffff', text: 'Video 6', filename: 'video-thumb-6.svg' },
  { bgColor: '#7c3aed', textColor: '#ffffff', text: 'Video 7', filename: 'video-thumb-7.svg' },
  { bgColor: '#8b5cf6', textColor: '#ffffff', text: 'Video 8', filename: 'video-thumb-8.svg' },
  { bgColor: '#a78bfa', textColor: '#ffffff', text: 'Video 9', filename: 'video-thumb-9.svg' },
  { bgColor: '#c4b5fd', textColor: '#000000', text: 'Video 10', filename: 'video-thumb-10.svg' },
  { bgColor: '#1e40af', textColor: '#ffffff', text: 'Video 11', filename: 'video-thumb-11.svg' },
  { bgColor: '#2563eb', textColor: '#ffffff', text: 'Video 12', filename: 'video-thumb-12.svg' }
];

// Category thumbnail placeholders
const categoryPlaceholders = [
  { bgColor: '#dc2626', textColor: '#ffffff', text: 'Popular', filename: 'category-1.svg' },
  { bgColor: '#ea580c', textColor: '#ffffff', text: 'Featured', filename: 'category-2.svg' },
  { bgColor: '#d97706', textColor: '#ffffff', text: 'Trending', filename: 'category-3.svg' },
  { bgColor: '#65a30d', textColor: '#ffffff', text: 'Premium', filename: 'category-4.svg' },
  { bgColor: '#059669', textColor: '#ffffff', text: 'New', filename: 'category-5.svg' },
  { bgColor: '#0891b2', textColor: '#ffffff', text: 'Hot', filename: 'category-6.svg' },
  { bgColor: '#2563eb', textColor: '#ffffff', text: 'Top', filename: 'category-7.svg' },
  { bgColor: '#7c3aed', textColor: '#ffffff', text: 'Best', filename: 'category-8.svg' }
];

// Generate all placeholders
console.log('Creating SVG placeholder images...');

// Create video thumbnails
console.log('Creating video thumbnails...');
videoPlaceholders.forEach(placeholder => {
  createSVGPlaceholder(640, 360, placeholder.bgColor, placeholder.textColor, placeholder.text, placeholder.filename);
});

// Create category thumbnails
console.log('Creating category thumbnails...');
categoryPlaceholders.forEach(placeholder => {
  createSVGPlaceholder(640, 360, placeholder.bgColor, placeholder.textColor, placeholder.text, placeholder.filename);
});

console.log('All SVG placeholder images created successfully!');
