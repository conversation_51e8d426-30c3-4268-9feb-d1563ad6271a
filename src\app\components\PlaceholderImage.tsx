'use client';

import { useState } from 'react';
import { FaPlay, FaImage } from 'react-icons/fa';
import { getVideoPlaceholder, getCategoryPlaceholder } from '@/app/utils/placeholders';

interface PlaceholderImageProps {
  src?: string;
  alt: string;
  className?: string;
  type?: 'video' | 'category';
  id?: string;
  title?: string;
  showPlayIcon?: boolean;
  fallbackText?: string;
}

const PlaceholderImage: React.FC<PlaceholderImageProps> = ({
  src,
  alt,
  className = '',
  type = 'video',
  id,
  title,
  showPlayIcon = false,
  fallbackText
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get appropriate placeholder based on type
  const getPlaceholder = () => {
    if (type === 'category') {
      return getCategoryPlaceholder(id, title);
    }
    return getVideoPlaceholder(id, title);
  };

  const finalSrc = src || getPlaceholder();

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  // If image failed to load, show a gradient placeholder
  if (imageError) {
    return (
      <div className={`relative bg-gradient-to-br from-purple-900 to-blue-900 flex items-center justify-center ${className}`}>
        <div className="text-center text-white">
          <FaImage className="mx-auto mb-2 text-2xl opacity-60" />
          <p className="text-sm opacity-80">{fallbackText || alt}</p>
          {showPlayIcon && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                <FaPlay className="text-white ml-1" />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center">
          <div className="text-gray-400">
            <FaImage className="text-2xl" />
          </div>
        </div>
      )}
      <img
        src={finalSrc}
        alt={alt}
        className={`w-full h-full object-cover ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
      />
      {showPlayIcon && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-12 h-12 rounded-full bg-black/50 flex items-center justify-center hover:bg-black/70 transition-colors duration-300">
            <FaPlay className="text-white ml-1" />
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaceholderImage;
