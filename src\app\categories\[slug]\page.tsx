'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import VideoCard from '@/app/components/VideoCard';
import { FaFilter, FaSpinner } from 'react-icons/fa';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  thumbnailUrl: string;
  isActive: boolean;
}

interface Video {
  _id: string;
  title: string;
  thumbnailUrl: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
}

export default function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {
  const router = useRouter();
  const { slug } = use(params);
  const [sortBy, setSortBy] = useState('newest');
  const [category, setCategory] = useState<Category | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalVideos, setTotalVideos] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch category and its videos
  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        setLoading(true);

        // Fetch category details
        const categoryResponse = await axios.get(`/api/categories/${slug}`);

        if (categoryResponse.data && categoryResponse.data.category) {
          setCategory(categoryResponse.data.category);

          // Fetch videos for this category
          const params = new URLSearchParams();
          params.append('category', categoryResponse.data.category._id);
          params.append('sort', sortBy);
          params.append('page', page.toString());
          params.append('limit', '12');

          const videosResponse = await axios.get(`/api/videos?${params.toString()}`);

          if (videosResponse.data) {
            setVideos(videosResponse.data.videos || []);
            setTotalVideos(videosResponse.data.pagination?.total || 0);
            setTotalPages(videosResponse.data.pagination?.totalPages || 1);
          }
        } else {
          setError('Category not found');
        }
      } catch (error) {
        console.error('Error fetching category data:', error);
        setError('Failed to load category data');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchCategoryData();
    }
  }, [slug, sortBy, page]);

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSortBy(e.target.value);
    setPage(1); // Reset to first page when changing sort
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    // Scroll to top when changing page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading) {
    return (
      <div className="w-full min-h-[60vh] flex items-center justify-center">
        <div className="text-gray-400 text-xl flex items-center">
          <FaSpinner className="animate-spin mr-2" /> Loading category...
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="w-full min-h-[60vh] flex flex-col items-center justify-center">
        <div className="text-red-500 text-xl mb-4">{error || 'Category not found'}</div>
        <button
          onClick={() => router.push('/categories')}
          className="btn-primary"
        >
          View All Categories
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">{category.name} Videos</h1>
        <p className="text-gray-400">{category.description}</p>
      </div>

      {/* Filters */}
      <div className="flex justify-between items-center mb-6">
        <div className="text-gray-400">
          <span>{totalVideos} videos</span>
        </div>

        <div className="flex items-center">
          <FaFilter className="text-red-500 mr-2" />
          <select
            className="bg-gray-800 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
            value={sortBy}
            onChange={handleSortChange}
          >
            <option value="newest">Newest</option>
            <option value="most-viewed">Most Viewed</option>
            <option value="most-liked">Most Liked</option>
            <option value="duration-asc">Shortest</option>
            <option value="duration-desc">Longest</option>
          </select>
        </div>
      </div>

      {/* Videos Grid */}
      {videos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {videos.map((video) => (
            <VideoCard
              key={video._id}
              id={video._id}
              title={video.title}
              thumbnail={video.thumbnailUrl}
              duration={video.duration}
              views={video.views}
              likes={video.likes}
              slug={video.slug}
            />
          ))}
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-white mb-2">No videos found</h3>
          <p className="text-gray-400">There are no videos in this category yet.</p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-4 py-2 rounded-lg ${
                page === 1
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around the current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-4 py-2 rounded-lg ${
                    page === pageNum
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-800 text-white hover:bg-gray-700'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-4 py-2 rounded-lg ${
                page === totalPages
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
