import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';

export async function POST(req: NextRequest) {
  try {
    await dbConnect();

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ isAdmin: true });
    
    if (existingAdmin) {
      return NextResponse.json(
        { message: 'Admin user already exists', email: existingAdmin.email },
        { status: 200 }
      );
    }

    // Create admin user
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      isAdmin: true,
      isVerified: true,
      dateOfBirth: new Date('1990-01-01')
    });

    await adminUser.save();

    return NextResponse.json(
      { 
        message: 'Admin user created successfully',
        email: '<EMAIL>',
        password: 'admin123'
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    await dbConnect();

    // Check if admin user exists
    const adminUser = await User.findOne({ isAdmin: true });
    
    if (adminUser) {
      return NextResponse.json(
        { 
          exists: true,
          email: adminUser.email,
          username: adminUser.username
        },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { exists: false },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error('Error checking admin user:', error);
    return NextResponse.json(
      { error: 'Failed to check admin user' },
      { status: 500 }
    );
  }
}
