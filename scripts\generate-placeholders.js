const fs = require('fs');
const path = require('path');
const https = require('https');

// Create placeholders directory if it doesn't exist
const placeholdersDir = path.join(__dirname, '../public/images/placeholders');
if (!fs.existsSync(placeholdersDir)) {
  fs.mkdirSync(placeholdersDir, { recursive: true });
}

// Function to download image from URL
function downloadImage(url, filename) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(path.join(placeholdersDir, filename));
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filename}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(path.join(placeholdersDir, filename), () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Generate video thumbnail placeholders
const videoPlaceholders = [
  {
    url: 'https://via.placeholder.com/640x360/1a1a1a/ffffff?text=Video+Thumbnail+1',
    filename: 'video-thumb-1.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/2d1b69/ffffff?text=Video+Thumbnail+2',
    filename: 'video-thumb-2.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/3730a3/ffffff?text=Video+Thumbnail+3',
    filename: 'video-thumb-3.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/4c1d95/ffffff?text=Video+Thumbnail+4',
    filename: 'video-thumb-4.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/5b21b6/ffffff?text=Video+Thumbnail+5',
    filename: 'video-thumb-5.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/6d28d9/ffffff?text=Video+Thumbnail+6',
    filename: 'video-thumb-6.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/7c3aed/ffffff?text=Video+Thumbnail+7',
    filename: 'video-thumb-7.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/8b5cf6/ffffff?text=Video+Thumbnail+8',
    filename: 'video-thumb-8.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/a78bfa/ffffff?text=Video+Thumbnail+9',
    filename: 'video-thumb-9.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/c4b5fd/000000?text=Video+Thumbnail+10',
    filename: 'video-thumb-10.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/1e40af/ffffff?text=Video+Thumbnail+11',
    filename: 'video-thumb-11.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/2563eb/ffffff?text=Video+Thumbnail+12',
    filename: 'video-thumb-12.jpg'
  }
];

// Generate category thumbnail placeholders
const categoryPlaceholders = [
  {
    url: 'https://via.placeholder.com/640x360/dc2626/ffffff?text=Popular+Category',
    filename: 'category-1.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/ea580c/ffffff?text=Featured+Category',
    filename: 'category-2.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/d97706/ffffff?text=Trending+Category',
    filename: 'category-3.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/65a30d/ffffff?text=Premium+Category',
    filename: 'category-4.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/059669/ffffff?text=New+Category',
    filename: 'category-5.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/0891b2/ffffff?text=Hot+Category',
    filename: 'category-6.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/2563eb/ffffff?text=Top+Category',
    filename: 'category-7.jpg'
  },
  {
    url: 'https://via.placeholder.com/640x360/7c3aed/ffffff?text=Best+Category',
    filename: 'category-8.jpg'
  }
];

// Download all placeholders
async function generatePlaceholders() {
  console.log('Generating placeholder images...');
  
  try {
    // Download video thumbnails
    console.log('Downloading video thumbnails...');
    for (const placeholder of videoPlaceholders) {
      await downloadImage(placeholder.url, placeholder.filename);
    }
    
    // Download category thumbnails
    console.log('Downloading category thumbnails...');
    for (const placeholder of categoryPlaceholders) {
      await downloadImage(placeholder.url, placeholder.filename);
    }
    
    console.log('All placeholder images generated successfully!');
  } catch (error) {
    console.error('Error generating placeholders:', error);
  }
}

generatePlaceholders();
