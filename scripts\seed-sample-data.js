const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Define schemas directly since we can't import TS models easily
const CategorySchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true, trim: true },
  slug: { type: String, required: true, unique: true, lowercase: true, trim: true },
  description: { type: String, required: true, trim: true },
  thumbnailUrl: { type: String, required: true },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const VideoSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  slug: { type: String, required: true, unique: true, lowercase: true, trim: true },
  description: { type: String, required: true, trim: true },
  duration: { type: Number, required: true },
  views: { type: Number, default: 0 },
  likes: { type: Number, default: 0 },
  dislikes: { type: Number, default: 0 },
  thumbnailUrl: { type: String, required: true },
  tags: [{ type: String, trim: true }],
  categories: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Category' }],
  uploadedBy: { type: String, required: true },
  isPublished: { type: Boolean, default: false },
  isVerified: { type: Boolean, default: false }
}, { timestamps: true });

const Category = mongoose.models.Category || mongoose.model('Category', CategorySchema);
const Video = mongoose.models.Video || mongoose.model('Video', VideoSchema);

// Sample categories with our placeholder images
const sampleCategories = [
  {
    name: 'Popular',
    slug: 'popular',
    description: 'Most popular content on the platform',
    thumbnailUrl: '/images/placeholders/category-1.svg',
    isActive: true
  },
  {
    name: 'Featured',
    slug: 'featured',
    description: 'Featured premium content',
    thumbnailUrl: '/images/placeholders/category-2.svg',
    isActive: true
  },
  {
    name: 'Trending',
    slug: 'trending',
    description: 'Currently trending videos',
    thumbnailUrl: '/images/placeholders/category-3.svg',
    isActive: true
  },
  {
    name: 'Premium',
    slug: 'premium',
    description: 'Exclusive premium content',
    thumbnailUrl: '/images/placeholders/category-4.svg',
    isActive: true
  },
  {
    name: 'New Releases',
    slug: 'new-releases',
    description: 'Latest uploaded content',
    thumbnailUrl: '/images/placeholders/category-5.svg',
    isActive: true
  },
  {
    name: 'Hot',
    slug: 'hot',
    description: 'Hottest content right now',
    thumbnailUrl: '/images/placeholders/category-6.svg',
    isActive: true
  }
];

// Sample videos with our placeholder images
const sampleVideos = [
  {
    title: 'Amazing Video Experience 1',
    slug: 'amazing-video-experience-1',
    description: 'An incredible video experience that will captivate your attention.',
    duration: 720,
    views: 15420,
    likes: 1250,
    dislikes: 45,
    thumbnailUrl: '/images/placeholders/video-thumb-1.svg',
    tags: ['trending', 'popular', 'featured'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Incredible Content 2',
    slug: 'incredible-content-2',
    description: 'Premium quality content with stunning visuals.',
    duration: 1080,
    views: 23150,
    likes: 1890,
    dislikes: 32,
    thumbnailUrl: '/images/placeholders/video-thumb-2.svg',
    tags: ['premium', 'quality', 'featured'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Premium Experience 3',
    slug: 'premium-experience-3',
    description: 'Exclusive premium content for discerning viewers.',
    duration: 900,
    views: 18750,
    likes: 1567,
    dislikes: 28,
    thumbnailUrl: '/images/placeholders/video-thumb-3.svg',
    tags: ['premium', 'exclusive', 'trending'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Hot New Release 4',
    slug: 'hot-new-release-4',
    description: 'The latest hot release that everyone is talking about.',
    duration: 1200,
    views: 31200,
    likes: 2340,
    dislikes: 67,
    thumbnailUrl: '/images/placeholders/video-thumb-4.svg',
    tags: ['hot', 'new', 'trending'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Featured Content 5',
    slug: 'featured-content-5',
    description: 'Specially featured content with exceptional quality.',
    duration: 840,
    views: 19800,
    likes: 1678,
    dislikes: 41,
    thumbnailUrl: '/images/placeholders/video-thumb-5.svg',
    tags: ['featured', 'quality', 'popular'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Trending Video 6',
    slug: 'trending-video-6',
    description: 'Currently trending video with amazing content.',
    duration: 960,
    views: 27500,
    likes: 2100,
    dislikes: 55,
    thumbnailUrl: '/images/placeholders/video-thumb-6.svg',
    tags: ['trending', 'amazing', 'popular'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Popular Choice 7',
    slug: 'popular-choice-7',
    description: 'A popular choice among viewers worldwide.',
    duration: 780,
    views: 22300,
    likes: 1890,
    dislikes: 38,
    thumbnailUrl: '/images/placeholders/video-thumb-7.svg',
    tags: ['popular', 'choice', 'worldwide'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  },
  {
    title: 'Premium Selection 8',
    slug: 'premium-selection-8',
    description: 'Carefully selected premium content for you.',
    duration: 1140,
    views: 16700,
    likes: 1456,
    dislikes: 29,
    thumbnailUrl: '/images/placeholders/video-thumb-8.svg',
    tags: ['premium', 'selected', 'quality'],
    uploadedBy: 'admin',
    isPublished: true,
    isVerified: true
  }
];

async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data
    await Category.deleteMany({});
    await Video.deleteMany({});
    console.log('Cleared existing data');

    // Insert categories
    const insertedCategories = await Category.insertMany(sampleCategories);
    console.log(`Inserted ${insertedCategories.length} categories`);

    // Assign categories to videos randomly
    const videosWithCategories = sampleVideos.map(video => ({
      ...video,
      categories: [insertedCategories[Math.floor(Math.random() * insertedCategories.length)]._id]
    }));

    // Insert videos
    const insertedVideos = await Video.insertMany(videosWithCategories);
    console.log(`Inserted ${insertedVideos.length} videos`);

    console.log('Sample data seeded successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
