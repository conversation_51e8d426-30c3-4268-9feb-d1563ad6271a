import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const timeRange = searchParams.get('timeRange') || 'week';

    // Calculate date range based on timeRange parameter
    const now = new Date();
    let dateFilter: Date;

    switch (timeRange) {
      case 'today':
        dateFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
        break;
      case 'week':
        dateFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
        break;
      case 'month':
        dateFilter = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        break;
      case 'all':
      default:
        dateFilter = new Date(0); // Beginning of time
        break;
    }

    const skip = (page - 1) * limit;

    // Build aggregation pipeline for trending videos
    const pipeline = [
      // Match published and verified videos within date range
      {
        $match: {
          isPublished: true,
          isVerified: true,
          createdAt: { $gte: dateFilter }
        }
      },
      // Populate categories
      {
        $lookup: {
          from: 'categories',
          localField: 'categories',
          foreignField: '_id',
          as: 'categories'
        }
      },
      // Calculate trending score based on views, likes, and recency
      {
        $addFields: {
          trendingScore: {
            $add: [
              { $multiply: ['$views', 1] },           // Views weight: 1
              { $multiply: ['$likes', 10] },          // Likes weight: 10
              { $multiply: [                          // Recency bonus
                {
                  $divide: [
                    { $subtract: [new Date(), '$createdAt'] },
                    1000 * 60 * 60 * 24 // Convert to days
                  ]
                },
                -1 // Negative to give bonus to newer videos
              ]}
            ]
          }
        }
      },
      // Sort by trending score (highest first)
      { $sort: { trendingScore: -1, views: -1, createdAt: -1 } },
      // Skip and limit for pagination
      { $skip: skip },
      { $limit: limit + 1 }, // Get one extra to check if there are more
      // Project only needed fields
      {
        $project: {
          title: 1,
          slug: 1,
          description: 1,
          duration: 1,
          views: 1,
          likes: 1,
          dislikes: 1,
          thumbnailUrl: '$thumbnail',
          videoUrl: 1,
          embedUrl: 1,
          sourceUrl: 1,
          sourceSite: 1,
          categories: {
            _id: 1,
            name: 1,
            slug: 1
          },
          tags: 1,
          uploadedBy: 1,
          isPublished: 1,
          isVerified: 1,
          createdAt: 1,
          updatedAt: 1,
          trendingScore: 1
        }
      }
    ];

    const results = await Video.aggregate(pipeline);
    
    // Check if there are more videos
    const hasMore = results.length > limit;
    const videos = hasMore ? results.slice(0, limit) : results;

    // Get total count for the time range (for statistics)
    const totalCount = await Video.countDocuments({
      isPublished: true,
      isVerified: true,
      createdAt: { $gte: dateFilter }
    });

    return NextResponse.json({
      success: true,
      videos,
      pagination: {
        currentPage: page,
        limit,
        hasMore,
        totalInRange: totalCount
      },
      timeRange,
      message: `Found ${videos.length} trending videos for ${timeRange}`
    });

  } catch (error) {
    console.error('Error fetching trending videos:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch trending videos',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
