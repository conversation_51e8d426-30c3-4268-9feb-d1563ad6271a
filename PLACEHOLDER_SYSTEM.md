# Placeholder Image System

This document describes the placeholder image system implemented for the adult video streaming website.

## Overview

The placeholder system provides consistent, themed placeholder images for videos and categories when actual thumbnails are not available. It uses SVG-based placeholders with a purple/blue color scheme that matches the website's design.

## Features

- **Consistent Placeholders**: Deterministic placeholder selection based on content ID or title
- **SVG-Based**: Scalable vector graphics for crisp display at any size
- **Themed Colors**: Purple/blue gradient theme matching the website design
- **Fallback System**: Multiple levels of fallback for robust image display
- **Performance**: Lightweight SVG files with minimal loading time

## File Structure

```
public/images/placeholders/
├── video-thumb-1.svg through video-thumb-12.svg  # Video thumbnails
└── category-1.svg through category-8.svg         # Category thumbnails
```

## Usage

### Video Thumbnails

```typescript
import { getVideoPlaceholder } from '@/app/utils/placeholders';

// Get a consistent placeholder for a video
const placeholder = getVideoPlaceholder(videoId, videoTitle);
```

### Category Thumbnails

```typescript
import { getCategoryPlaceholder } from '@/app/utils/placeholders';

// Get a consistent placeholder for a category
const placeholder = getCategoryPlaceholder(categoryId, categoryName);
```

### PlaceholderImage Component

```typescript
import PlaceholderImage from '@/app/components/PlaceholderImage';

<PlaceholderImage
  src={thumbnailUrl}
  alt="Video title"
  type="video"
  id={videoId}
  title={videoTitle}
  showPlayIcon={true}
  className="aspect-video"
/>
```

## Scripts

### Generate Placeholders
```bash
npm run placeholders
```
Creates all SVG placeholder files in the public/images/placeholders directory.

### Seed Sample Data
```bash
npm run seed
```
Populates the database with sample videos and categories using the placeholder images.

## Placeholder Selection Algorithm

The system uses a simple hash function to ensure the same content always gets the same placeholder:

1. Create a seed string from the content ID or title
2. Generate a hash from the seed string
3. Use modulo operation to select from available placeholders
4. Return the corresponding placeholder path

## Customization

### Adding New Placeholders

1. Create new SVG files in `public/images/placeholders/`
2. Update the `SAMPLE_THUMBNAILS` arrays in `src/app/utils/placeholders.ts`
3. Regenerate placeholders if needed

### Changing Colors

Edit the `scripts/create-svg-placeholders.js` file to modify:
- Background colors (`bgColor`)
- Text colors (`textColor`)
- Gradient directions and stops

### Custom Placeholder Generation

The `generatePlaceholderUrl` function can create custom placeholder URLs:

```typescript
import { generatePlaceholderUrl } from '@/app/utils/placeholders';

const customPlaceholder = generatePlaceholderUrl(
  640,           // width
  360,           // height
  'Custom Text', // text
  '1a1a1a',     // background color
  'ffffff'       // text color
);
```

## Sample Data

The system includes sample data for development:

- **Categories**: 6 sample categories with themed placeholders
- **Videos**: 8 sample videos with varied metadata
- **Consistent Theming**: All placeholders follow the purple/blue color scheme

## Error Handling

The `PlaceholderImage` component provides robust error handling:

1. **Loading State**: Shows animated placeholder while loading
2. **Error Fallback**: Displays gradient background if image fails
3. **Graceful Degradation**: Always shows something meaningful to users

## Performance Considerations

- **SVG Format**: Small file sizes (typically 1-2KB each)
- **Lazy Loading**: Images load only when needed
- **Caching**: Browser caches SVG files efficiently
- **No External Dependencies**: All placeholders are self-hosted

## Development Workflow

1. **Initial Setup**: Run `npm run placeholders` to create placeholder files
2. **Database Seeding**: Run `npm run seed` to populate with sample data
3. **Development**: Placeholders automatically used when thumbnails missing
4. **Production**: Replace with actual thumbnails as content is uploaded

## Integration Points

The placeholder system integrates with:

- **VideoCard Component**: Automatic placeholder selection
- **CategoryCard Component**: Consistent category thumbnails
- **VideoThumbnail Component**: Fallback system for missing images
- **Admin Interface**: Placeholder display in management views
- **API Responses**: Placeholder URLs in JSON responses when needed
