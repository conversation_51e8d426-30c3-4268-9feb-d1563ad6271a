import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import Category from '@/app/models/Category';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Await params to get the slug
    const { slug } = await params;
    console.log('API: Looking for video with slug:', slug);

    // Connect to the database
    await dbConnect();

    // Debug: List all video slugs
    const allSlugs = await Video.find({}, 'slug title').limit(10);
    console.log('API: Available video slugs:', allSlugs.map(v => ({ slug: v.slug, title: v.title })));

    // Debug: Check the specific video without isActive filter
    const videoDebug = await Video.findOne({ slug });
    console.log('API: Video debug info:', videoDebug ? {
      title: videoDebug.title,
      slug: videoDebug.slug,
      isActive: videoDebug.isActive,
      isPublished: videoDebug.isPublished
    } : 'Not found');

    // Find video by slug (remove isActive filter temporarily)
    const video = await Video.findOne({ slug })
      .populate('categories', 'name slug');

    console.log('API: Found video without isActive filter:', video ? video.title : 'Not found');

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Don't increment view count here, we'll do it in a separate endpoint

    // Find related videos
    const relatedVideos = await Video.find({
      _id: { $ne: video._id },
      $or: [
        { categories: { $in: video.categories } },
        { tags: { $in: video.tags } }
      ],
      isActive: true
    })
      .limit(6)
      .sort({ views: -1 })
      .select('title thumbnail duration views likes slug');

    // Transform the video data to match frontend expectations
    const transformedVideo = {
      ...video.toObject(),
      thumbnailUrl: video.thumbnail, // Map thumbnail to thumbnailUrl
      videoUrl: video.embedUrl || video.videoUrl // Ensure videoUrl is available
    };

    // Transform related videos
    const transformedRelatedVideos = relatedVideos.map(v => ({
      ...v.toObject(),
      thumbnailUrl: v.thumbnail
    }));

    return NextResponse.json({
      video: transformedVideo,
      relatedVideos: transformedRelatedVideos
    });
  } catch (error) {
    console.error('Error fetching video:', error);
    return NextResponse.json(
      { error: 'Failed to fetch video' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Await params to get the slug
    const { slug } = await params;
    const body = await req.json();

    // Connect to the database
    await dbConnect();

    // Find and update video
    const video = await Video.findOneAndUpdate(
      { slug },
      body,
      { new: true, runValidators: true }
    );

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(video);
  } catch (error) {
    console.error('Error updating video:', error);
    return NextResponse.json(
      { error: 'Failed to update video' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const slug = params.slug;

    // Connect to the database
    await dbConnect();

    // Find and delete video
    const video = await Video.findOneAndDelete({ slug });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    return NextResponse.json(
      { error: 'Failed to delete video' },
      { status: 500 }
    );
  }
}
