'use client';

import { useState, useEffect } from 'react';
import { FaPlus, FaSearch, FaEdit, FaTrash, FaEye, FaThumbsUp } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';
import axios from 'axios';

interface Video {
  _id: string;
  title: string;
  thumbnail: string;
  duration: number;
  views: number;
  likes: number;
  categories: Array<{ _id: string; name: string; slug: string }>;
  isActive?: boolean;
  isPublished: boolean;
  createdAt: string;
  slug: string;
}

// Format duration from seconds to MM:SS
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

// Format views count (e.g., 1.2K, 3.5M)
const formatCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

export default function AdminVideos() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // Fetch videos from API
  useEffect(() => {
    fetchVideos();
  }, []);

  const fetchVideos = async () => {
    try {
      setLoading(true);
      // Fetch all videos for admin (including inactive ones)
      const response = await axios.get('/api/admin/videos');

      if (response.data && response.data.videos) {
        setVideos(response.data.videos);
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      setError('Failed to load videos');
    } finally {
      setLoading(false);
    }
  };

  // Get unique categories from all videos
  const categories = ['all', ...new Set(videos.flatMap(video =>
    video.categories.map(cat => cat.name)
  ))].sort();

  // Filter videos based on search query, selected category, and status
  const filteredVideos = videos.filter(video => {
    const matchesQuery = video.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' ||
      video.categories.some(cat => cat.name === selectedCategory);
    const matchesStatus = statusFilter === 'all' ||
                          (statusFilter === 'active' && (video.isActive !== false)) ||
                          (statusFilter === 'inactive' && (video.isActive === false));
    return matchesQuery && matchesCategory && matchesStatus;
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Manage Videos</h1>
        <Link href="/admin/videos/add" className="btn-primary flex items-center">
          <FaPlus className="mr-2" /> Add New Video
        </Link>
      </div>
      
      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <form onSubmit={handleSearch}>
            <div className="flex">
              <input
                type="text"
                placeholder="Search videos..."
                className="input-field flex-grow"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button type="submit" className="ml-2 p-2 bg-orange-500 rounded hover:bg-orange-600">
                <FaSearch />
              </button>
            </div>
          </form>
          
          <div>
            <select 
              className="input-field w-full"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="all">All Categories</option>
              {categories.filter(cat => cat !== 'all').map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select 
              className="input-field w-full"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Loading State */}
      {loading && (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <div className="text-white">Loading videos...</div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-4 mb-6">
          <div className="text-red-200">{error}</div>
          <button
            onClick={fetchVideos}
            className="mt-2 px-4 py-2 bg-red-700 text-white rounded hover:bg-red-600"
          >
            Retry
          </button>
        </div>
      )}

      {/* Videos Table */}
      {!loading && !error && (
        <div className="bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-700">
                  <th className="px-4 py-3 text-left text-white">Video</th>
                  <th className="px-4 py-3 text-left text-white">Categories</th>
                  <th className="px-4 py-3 text-left text-white">Stats</th>
                  <th className="px-4 py-3 text-left text-white">Status</th>
                  <th className="px-4 py-3 text-left text-white">Date</th>
                  <th className="px-4 py-3 text-left text-white">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredVideos.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center text-gray-400">
                      No videos found
                    </td>
                  </tr>
                ) : (
                  filteredVideos.map((video) => (
                    <tr key={video._id} className="border-t border-gray-700">
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="relative w-20 h-12 mr-3">
                        <Image
                          src={video.thumbnail || '/images/placeholders/video-thumb-1.svg'}
                          alt={video.title}
                          fill
                          className="object-cover rounded"
                        />
                        <div className="absolute bottom-0 right-0 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                          {formatDuration(video.duration)}
                        </div>
                      </div>
                      <span className="text-white">{video.title}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex flex-wrap gap-1">
                      {video.categories.map((category) => (
                        <span key={category._id} className="bg-gray-700 text-xs text-gray-300 px-2 py-1 rounded">
                          {category.name}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="text-gray-400 text-sm">
                      <div className="flex items-center mb-1">
                        <FaEye className="mr-1" /> {formatCount(video.views)} views
                      </div>
                      <div className="flex items-center">
                        <FaThumbsUp className="mr-1" /> {formatCount(video.likes)} likes
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${(video.isActive !== false) ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                      {(video.isActive !== false) ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-gray-400">
                    {new Date(video.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <Link href={`/videos/${video.slug}`} className="p-2 bg-green-600 text-white rounded hover:bg-green-700" title="View Video">
                        <FaEye />
                      </Link>
                      <Link href={`/admin/videos/edit/${video._id}`} className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700" title="Edit Video">
                        <FaEdit />
                      </Link>
                      <button
                        className="p-2 bg-red-600 text-white rounded hover:bg-red-700"
                        title="Delete Video"
                        onClick={() => {
                          if (confirm('Are you sure you want to delete this video?')) {
                            // TODO: Implement delete functionality
                            console.log('Delete video:', video._id);
                          }
                        }}
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
